//
//  DailyPromptManager.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import Foundation

class DailyPromptManager {
    static let shared = DailyPromptManager()

    private init() {}

    // MARK: - Daily Prompt Generation

    func getTodaysPrompt(category: PromptCategory? = nil) -> DailyPrompt {
        let today = Date()
        let calendar = Calendar.current
        let dayOfYear = calendar.ordinality(of: .day, in: .year, for: today) ?? 1
        let hour = calendar.component(.hour, from: today)

        // Add time-based variation for more fun
        let timeMultiplier = getTimeMultiplier(for: hour)
        let seedValue = (dayOfYear * timeMultiplier) % 1000

        let availablePrompts: [DailyPrompt]
        if let category = category {
            availablePrompts = allPrompts.filter { $0.category == category }
        } else {
            availablePrompts = allPrompts
        }

        // Use enhanced seed for more variety
        let promptIndex = seedValue % availablePrompts.count
        return availablePrompts[promptIndex]
    }

    private func getTimeMultiplier(for hour: Int) -> Int {
        switch hour {
        case 6...11:  // Morning
            return 3
        case 12...17: // Afternoon
            return 7
        case 18...22: // Evening
            return 11
        default:      // Night/Early morning
            return 13
        }
    }

    func getRandomPrompt() -> DailyPrompt {
        return allPrompts.randomElement() ?? allPrompts[0]
    }

    func getPromptForCategory(_ category: PromptCategory) -> DailyPrompt {
        let categoryPrompts = allPrompts.filter { $0.category == category }
        return categoryPrompts.randomElement() ?? allPrompts[0]
    }

    // MARK: - Prompt Database

    private let allPrompts: [DailyPrompt] = [
        // Gratitude & Positivity
        DailyPrompt(emoji: "✨", prompt: "What made you smile today?", category: .gratitude),
        DailyPrompt(emoji: "🌟", prompt: "What are three things you're grateful for right now?", category: .gratitude),
        DailyPrompt(emoji: "💫", prompt: "Describe a moment that filled your heart with joy.", category: .gratitude),
        DailyPrompt(emoji: "🌈", prompt: "What's the best compliment you received recently?", category: .gratitude),
        DailyPrompt(emoji: "☀️", prompt: "How did you spread kindness today?", category: .gratitude),

        // Self-Reflection
        DailyPrompt(emoji: "🤔", prompt: "What did you learn about yourself today?", category: .reflection),
        DailyPrompt(emoji: "🎯", prompt: "What's one goal you're excited to work on?", category: .reflection),
        DailyPrompt(emoji: "🌱", prompt: "How have you grown in the past month?", category: .reflection),
        DailyPrompt(emoji: "🔍", prompt: "What pattern in your life would you like to change?", category: .reflection),
        DailyPrompt(emoji: "💭", prompt: "What's been on your mind lately?", category: .reflection),

        // Creativity & Dreams
        DailyPrompt(emoji: "🎨", prompt: "If you could create anything, what would it be?", category: .creativity),
        DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
        DailyPrompt(emoji: "🎭", prompt: "If you could live in any fictional world, which would you choose?", category: .creativity),
        DailyPrompt(emoji: "🎪", prompt: "What would your perfect day look like?", category: .creativity),
        DailyPrompt(emoji: "🎵", prompt: "What song perfectly describes your current mood?", category: .creativity),

        // Memories & Experiences
        DailyPrompt(emoji: "📸", prompt: "What's your favorite memory from this week?", category: .memories),
        DailyPrompt(emoji: "🎈", prompt: "Describe a moment when you felt truly alive.", category: .memories),
        DailyPrompt(emoji: "🌺", prompt: "What's the most beautiful thing you saw today?", category: .memories),
        DailyPrompt(emoji: "🎁", prompt: "What unexpected gift did life give you recently?", category: .memories),
        DailyPrompt(emoji: "🌙", prompt: "What's a childhood memory that still makes you happy?", category: .memories),

        // Adventure & Fun
        DailyPrompt(emoji: "🗺️", prompt: "Where would you go if you could teleport anywhere right now?", category: .adventure),
        DailyPrompt(emoji: "🎢", prompt: "What's the most spontaneous thing you've done lately?", category: .adventure),
        DailyPrompt(emoji: "🏔️", prompt: "What adventure is calling your name?", category: .adventure),
        DailyPrompt(emoji: "🎯", prompt: "What's something new you'd like to try this month?", category: .adventure),
        DailyPrompt(emoji: "🌊", prompt: "If you could master any skill instantly, what would it be?", category: .adventure),

        // Relationships & Love
        DailyPrompt(emoji: "💝", prompt: "Who made your day better just by being in it?", category: .relationships),
        DailyPrompt(emoji: "🤗", prompt: "What's something you love about your best friend?", category: .relationships),
        DailyPrompt(emoji: "💌", prompt: "What would you tell your younger self?", category: .relationships),
        DailyPrompt(emoji: "🌻", prompt: "How did someone show you love today?", category: .relationships),
        DailyPrompt(emoji: "💕", prompt: "What's your favorite way to show someone you care?", category: .relationships),

        // Quirky & Fun
        DailyPrompt(emoji: "🦄", prompt: "If animals could talk, which would be the rudest?", category: .quirky),
        DailyPrompt(emoji: "🍕", prompt: "What's the weirdest food combination you actually enjoy?", category: .quirky),
        DailyPrompt(emoji: "🎪", prompt: "If you had a superpower for just one day, what would you do?", category: .quirky),
        DailyPrompt(emoji: "🎭", prompt: "What's your most embarrassing moment that's now funny?", category: .quirky),
        DailyPrompt(emoji: "🎨", prompt: "If your life was a movie, what genre would it be?", category: .quirky),
        DailyPrompt(emoji: "🤖", prompt: "What would you name your pet robot?", category: .quirky),
        DailyPrompt(emoji: "🎯", prompt: "If you could only eat one food for the rest of your life, what would it be?", category: .quirky),
        DailyPrompt(emoji: "🎲", prompt: "What's the most random thing in your bag right now?", category: .quirky),
        DailyPrompt(emoji: "🎈", prompt: "If you could swap lives with anyone for a day, who would it be?", category: .quirky),
        DailyPrompt(emoji: "🎊", prompt: "What's your weirdest talent or skill?", category: .quirky),

        // Seasonal & Weather
        DailyPrompt(emoji: "🌸", prompt: "How does today's weather match your mood?", category: .seasonal),
        DailyPrompt(emoji: "🍂", prompt: "What's your favorite thing about this season?", category: .seasonal),
        DailyPrompt(emoji: "❄️", prompt: "What cozy activity makes you feel most at peace?", category: .seasonal),
        DailyPrompt(emoji: "🌞", prompt: "How do you like to spend sunny days?", category: .seasonal),
        DailyPrompt(emoji: "🌧️", prompt: "What's perfect about a rainy day?", category: .seasonal),

        // Goals & Motivation
        DailyPrompt(emoji: "🎯", prompt: "What small win can you celebrate today?", category: .motivation),
        DailyPrompt(emoji: "💪", prompt: "What challenge are you ready to tackle?", category: .motivation),
        DailyPrompt(emoji: "🌟", prompt: "What's one thing you're proud of accomplishing?", category: .motivation),
        DailyPrompt(emoji: "🚀", prompt: "What's motivating you to keep going?", category: .motivation),
        DailyPrompt(emoji: "✨", prompt: "How will you make tomorrow even better?", category: .motivation),

        // Mindfulness & Present
        DailyPrompt(emoji: "🧘", prompt: "What are you feeling in this exact moment?", category: .mindfulness),
        DailyPrompt(emoji: "🌿", prompt: "What sounds, smells, or textures are you noticing right now?", category: .mindfulness),
        DailyPrompt(emoji: "💆", prompt: "How are you taking care of yourself today?", category: .mindfulness),
        DailyPrompt(emoji: "🌸", prompt: "What's something beautiful you can see from where you are?", category: .mindfulness),
        DailyPrompt(emoji: "🕯️", prompt: "What brings you peace when life feels chaotic?", category: .mindfulness)
    ]
}

// MARK: - Data Models

struct DailyPrompt: Codable, Identifiable {
    let id: UUID
    let emoji: String
    let prompt: String
    let category: PromptCategory

    init(emoji: String, prompt: String, category: PromptCategory) {
        self.id = UUID()
        self.emoji = emoji
        self.prompt = prompt
        self.category = category
    }

    var displayText: String {
        return "\(emoji) \(prompt)"
    }

    var timeBasedGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5...11:
            return "Good morning! ☀️"
        case 12...17:
            return "Good afternoon! 🌤️"
        case 18...21:
            return "Good evening! 🌅"
        default:
            return "Good night! 🌙"
        }
    }
}

enum PromptCategory: String, CaseIterable, Codable {
    case gratitude = "Gratitude"
    case reflection = "Reflection"
    case creativity = "Creativity"
    case memories = "Memories"
    case adventure = "Adventure"
    case relationships = "Relationships"
    case quirky = "Quirky"
    case seasonal = "Seasonal"
    case motivation = "Motivation"
    case mindfulness = "Mindfulness"

    var color: String {
        switch self {
        case .gratitude: return "#FFD700"      // Gold
        case .reflection: return "#9370DB"     // Medium Purple
        case .creativity: return "#FF69B4"     // Hot Pink
        case .memories: return "#87CEEB"       // Sky Blue
        case .adventure: return "#32CD32"      // Lime Green
        case .relationships: return "#FF6347"  // Tomato
        case .quirky: return "#FF8C00"         // Dark Orange
        case .seasonal: return "#20B2AA"       // Light Sea Green
        case .motivation: return "#DC143C"     // Crimson
        case .mindfulness: return "#9ACD32"    // Yellow Green
        }
    }
}
