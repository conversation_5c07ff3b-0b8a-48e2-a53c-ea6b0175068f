//
//  AppIntent.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import WidgetKit
import AppIntents

@available(iOS 17.0, *)
struct ConfigurationAppIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource { "Daily Prompt Settings" }
    static var description: IntentDescription { "Customize your daily journal prompts." }

    @Parameter(title: "Prompt Category", default: .all)
    var promptCategory: PromptCategoryOption
}

@available(iOS 17.0, *)
enum PromptCategoryOption: String, AppEnum, CaseIterable {
    case all = "All Categories"
    case gratitude = "Gratitude"
    case reflection = "Reflection"
    case creativity = "Creativity"
    case memories = "Memories"
    case adventure = "Adventure"
    case relationships = "Relationships"
    case quirky = "Quirky"
    case seasonal = "Seasonal"
    case motivation = "Motivation"
    case mindfulness = "Mindfulness"

    static var typeDisplayRepresentation: TypeDisplayRepresentation {
        TypeDisplayRepresentation(name: "Prompt Category")
    }

    static var caseDisplayRepresentations: [PromptCategoryOption: DisplayRepresentation] {
        [
            .all: DisplayRepresentation(title: "All Categories", subtitle: "Mix of all prompt types"),
            .gratitude: DisplayRepresentation(title: "Gratitude", subtitle: "Focus on thankfulness"),
            .reflection: DisplayRepresentation(title: "Reflection", subtitle: "Self-discovery prompts"),
            .creativity: DisplayRepresentation(title: "Creativity", subtitle: "Imaginative questions"),
            .memories: DisplayRepresentation(title: "Memories", subtitle: "Cherished moments"),
            .adventure: DisplayRepresentation(title: "Adventure", subtitle: "Exciting experiences"),
            .relationships: DisplayRepresentation(title: "Relationships", subtitle: "Love and connections"),
            .quirky: DisplayRepresentation(title: "Quirky", subtitle: "Fun and silly prompts"),
            .seasonal: DisplayRepresentation(title: "Seasonal", subtitle: "Weather and seasons"),
            .motivation: DisplayRepresentation(title: "Motivation", subtitle: "Goals and achievements"),
            .mindfulness: DisplayRepresentation(title: "Mindfulness", subtitle: "Present moment awareness")
        ]
    }
}
