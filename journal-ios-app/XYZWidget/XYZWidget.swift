//
//  XYZWidget.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/1.
//

import WidgetKit
import SwiftUI
import AppIntents
import Foundation

// MARK: - iOS 17+ Provider (with AppIntents support)
@available(iOS 17.0, *)
struct AppIntentProvider: AppIntentTimelineProvider {
    typealias Entry = SimpleEntry

    func placeholder(in context: Context) -> SimpleEntry {
        let placeholderPrompt = DailyPrompt(
            emoji: "✨",
            prompt: "What made you smile today?",
            category: .gratitude
        )
        let placeholderData = WidgetDisplayData(
            motivationalMessage: "Your thoughts matter! 💭",
            hasWrittenToday: false,
            totalEntries: 12,
            todaysJournalEntry: nil,
            isNewUser: false,
            timeOfDay: .morning
        )
        return SimpleEntry(date: Date(), configuration: ConfigurationAppIntent(), dailyPrompt: placeholderPrompt, widgetData: placeholderData)
    }

    func snapshot(for configuration: ConfigurationAppIntent, in context: Context) async -> SimpleEntry {
        let prompt = DailyPromptManager.shared.getTodaysPrompt()
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()
        return SimpleEntry(date: Date(), configuration: configuration, dailyPrompt: prompt, widgetData: widgetData)
    }

    func timeline(for configuration: ConfigurationAppIntent, in context: Context) async -> Timeline<SimpleEntry> {
        let currentDate = Date()
        let prompt = DailyPromptManager.shared.getTodaysPrompt()
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()

        // Create entry for today
        let entry = SimpleEntry(date: currentDate, configuration: configuration, dailyPrompt: prompt, widgetData: widgetData)

        // Update at midnight for new daily prompt and also update more frequently for time-based changes
        let calendar = Calendar.current
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        let nextMidnight = calendar.startOfDay(for: tomorrow)

        // Also update at 6 PM for evening view
        let today = calendar.startOfDay(for: currentDate)
        let sixPM = calendar.date(bySettingHour: 18, minute: 0, second: 0, of: today)!

        var nextUpdate = nextMidnight
        if currentDate < sixPM {
            nextUpdate = sixPM
        }

        return Timeline(entries: [entry], policy: .after(nextUpdate))
    }
}

// MARK: - iOS 16+ Provider (without AppIntents support)
@available(iOS 16.0, *)
struct LegacyProvider: TimelineProvider {
    typealias Entry = SimpleEntry

    func placeholder(in context: Context) -> SimpleEntry {
        let placeholderPrompt = DailyPrompt(
            emoji: "✨",
            prompt: "What made you smile today?",
            category: .gratitude
        )
        let placeholderData = WidgetDisplayData(
            motivationalMessage: "Your thoughts matter! 💭",
            hasWrittenToday: false,
            totalEntries: 12,
            todaysJournalEntry: nil,
            isNewUser: false,
            timeOfDay: .morning
        )
        return SimpleEntry(date: Date(), configuration: EmptyConfiguration(), dailyPrompt: placeholderPrompt, widgetData: placeholderData)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
        let prompt = DailyPromptManager.shared.getTodaysPrompt()
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()
        let entry = SimpleEntry(date: Date(), configuration: EmptyConfiguration(), dailyPrompt: prompt, widgetData: widgetData)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> Void) {
        let currentDate = Date()
        let prompt = DailyPromptManager.shared.getTodaysPrompt()
        let widgetData = WidgetDataManager.shared.getWidgetDisplayData()

        // Create entry for today
        let entry = SimpleEntry(date: currentDate, configuration: EmptyConfiguration(), dailyPrompt: prompt, widgetData: widgetData)

        // Update at midnight for new daily prompt and also update more frequently for time-based changes
        let calendar = Calendar.current
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        let nextMidnight = calendar.startOfDay(for: tomorrow)

        // Also update at 6 PM for evening view
        let today = calendar.startOfDay(for: currentDate)
        let sixPM = calendar.date(bySettingHour: 18, minute: 0, second: 0, of: today)!

        var nextUpdate = nextMidnight
        if currentDate < sixPM {
            nextUpdate = sixPM
        }

        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
}

// MARK: - Configuration Types
struct EmptyConfiguration {
    // Empty configuration for iOS 16 compatibility
}

// MARK: - Timeline Entry
struct SimpleEntry: TimelineEntry {
    let date: Date
    let configuration: Any // Can be ConfigurationAppIntent or EmptyConfiguration
    let dailyPrompt: DailyPrompt
    let widgetData: WidgetDisplayData

    init(date: Date, configuration: Any, dailyPrompt: DailyPrompt, widgetData: WidgetDisplayData) {
        self.date = date
        self.configuration = configuration
        self.dailyPrompt = dailyPrompt
        self.widgetData = widgetData
    }
}

struct XYZWidgetEntryView : View {
    var entry: SimpleEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        default:
            MediumWidgetView(entry: entry)
        }
    }
}

struct SmallWidgetView: View {
    let entry: SimpleEntry

    private var dayOfMonth: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d MMM"
        return formatter.string(from: entry.date)
    }

    private var dayOfWeek: String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "EEEE"
        return formatter.string(from: entry.date)
    }

    var body: some View {
        VStack(spacing: 0) {
            // Date section
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(dayOfWeek)
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        .lineLimit(1)

                    Text(dayOfMonth)
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                }
                Spacer()
            }

            Spacer(minLength: 0)

            // Prompt section
            VStack(alignment: .leading, spacing: 0) {
                Text(entry.dailyPrompt.prompt)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
            }
            Spacer(minLength: 0)
        }
        .padding(.horizontal, 0)
        .padding(.vertical, 5)
    }
}

struct MediumWidgetView: View {
    let entry: SimpleEntry

    private var dayOfMonth: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d MMM"
        return formatter.string(from: entry.date)
    }

    private var dayOfWeek: String {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        formatter.dateFormat = "EEEE"
        return formatter.string(from: entry.date)
    }

    var body: some View {
        HStack {
            // Left side - Date and emoji
            VStack(alignment: .leading, spacing: 8) {
                // Date section
                VStack(alignment: .center, spacing: 2) {
                    Text(dayOfWeek)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        .textCase(.uppercase)
                        .lineLimit(1)

                    Text(dayOfMonth)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                }

                Spacer()

                // Action button
                HStack(spacing: 1) {
                    Image(systemName: "pencil.circle.fill")
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        .font(.system(size: 14))

                    Text("Start Writing")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color(hex: entry.dailyPrompt.category.color).opacity(0.3), lineWidth: 1)
                        )
                )
            }

            // Right side - Prompt content
            VStack(alignment: .leading, spacing: 12) {
                // Header and prompt
                VStack(alignment: .leading, spacing: 6) {
                    Text(entry.dailyPrompt.prompt)
                        .font(.system(size: 15, weight: .medium, design: .rounded))
                        .lineLimit(3)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()
            }
        }
        .padding(.horizontal, 0)
    }
}

// MARK: - New User Prompt View
struct NewUserPromptView: View {
    let entry: SimpleEntry

    var body: some View {
        VStack(spacing: 16) {
            // Beautiful gradient background
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: entry.dailyPrompt.category.color).opacity(0.2),
                    Color(hex: entry.dailyPrompt.category.color).opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .overlay(
                VStack(spacing: 20) {
                    // Large emoji
                    Text(entry.dailyPrompt.emoji)
                        .font(.system(size: 60))
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)

                    // Prompt text
                    Text(entry.dailyPrompt.prompt)
                        .font(.system(size: 18, weight: .medium, design: .rounded))
                        .multilineTextAlignment(.center)
                        .foregroundColor(.primary)
                        .lineLimit(3)
                        .padding(.horizontal, 20)

                    // Welcome message
                    Text("Start your journaling journey! ✨")
                        .font(.subheadline)
                        .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        .fontWeight(.semibold)
                }
                .padding(20)
            )
        }
    }
}

// MARK: - Existing User Morning View
struct ExistingUserMorningView: View {
    let entry: SimpleEntry

    var body: some View {
        HStack(spacing: 20) {
            // Left side - Emoji, stats, and category
            VStack(spacing: 12) {
                Text(entry.dailyPrompt.emoji)
                    .font(.system(size: 44))
                    .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)

                Text(entry.dailyPrompt.category.rawValue.uppercased())
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.15))
                    )

                Spacer()

                // Stats
                VStack(spacing: 2) {
                    Text("\(entry.widgetData.totalEntries)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Text("entries")
                        .font(.caption2)
                        .foregroundColor(Color(.secondaryLabel))
                        .fontWeight(.medium)
                }
            }
            .frame(width: 90)

            // Right side - Prompt and action
            VStack(alignment: .leading, spacing: 14) {
                // Motivational message
                Text(entry.widgetData.motivationalMessage)
                    .font(.caption)
                    .foregroundColor(Color(.secondaryLabel))
                    .italic()

                Text(entry.dailyPrompt.prompt)
                    .font(.system(size: 17, weight: .medium, design: .rounded))
                    .lineLimit(nil)
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)

                Spacer()

                // Status and action
                VStack(alignment: .leading, spacing: 8) {
                    if entry.widgetData.hasWrittenToday {
                        HStack(spacing: 6) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.system(size: 16))
                            Text("You've written today!")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                        }
                        .padding(.horizontal, 14)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .fill(Color.green.opacity(0.12))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 22)
                                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                )
                        )
                    } else {
                        HStack(spacing: 6) {
                            Image(systemName: "pencil.circle.fill")
                                .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                                .font(.system(size: 16))

                            Text("Start writing in Journal")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(Color(hex: entry.dailyPrompt.category.color))
                        }
                        .padding(.horizontal, 14)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 22)
                                .fill(Color(hex: entry.dailyPrompt.category.color).opacity(0.12))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 22)
                                        .stroke(Color(hex: entry.dailyPrompt.category.color).opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                }
            }
        }
        .padding(20)
    }
}

// MARK: - Existing User Evening View
struct ExistingUserEveningView: View {
    let entry: SimpleEntry

    var body: some View {
        if let todaysEntry = entry.widgetData.todaysJournalEntry {
            // Show today's journal content
            HStack(spacing: 16) {
                // Left side - Image or mood emoji
                VStack {
                    if todaysEntry.hasImage, let imageURL = todaysEntry.firstImageURL, let url = URL(string: imageURL) {
                        AsyncImage(url: url) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray5))
                                .overlay(
                                    Image(systemName: "photo")
                                        .foregroundColor(.gray)
                                )
                        }
                        .frame(width: 80, height: 80)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    } else {
                        // Show mood emoji if no image
                        Text(todaysEntry.moodEmoji)
                            .font(.system(size: 50))
                            .frame(width: 80, height: 80)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                            )
                    }

                    Spacer()
                }

                // Right side - Journal content
                VStack(alignment: .leading, spacing: 12) {
                    // Time greeting
                    HStack {
                        Text("Good evening! 🌅")
                            .font(.caption)
                            .foregroundColor(Color(.secondaryLabel))
                            .italic()
                        Spacer()
                        Text(todaysEntry.formattedTime)
                            .font(.caption2)
                            .foregroundColor(Color(.tertiaryLabel))
                    }

                    // Journal title
                    Text(todaysEntry.title)
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .lineLimit(2)
                        .foregroundColor(.primary)

                    // Content snippet
                    Text(todaysEntry.contentSnippet)
                        .font(.system(size: 14))
                        .lineLimit(4)
                        .foregroundColor(Color(.secondaryLabel))

                    Spacer()

                    // View entry button
                    HStack(spacing: 6) {
                        Image(systemName: "book.fill")
                            .foregroundColor(.blue)
                            .font(.system(size: 14))
                        Text("View today's entry")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.blue.opacity(0.12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
            .padding(20)
        } else {
            // No journal entry today, show prompt
            VStack(spacing: 16) {
                Text("🌅")
                    .font(.system(size: 50))

                Text("Good evening!")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.primary)

                Text("You haven't written today. How about reflecting on your day?")
                    .font(.system(size: 14))
                    .multilineTextAlignment(.center)
                    .foregroundColor(Color(.secondaryLabel))
                    .lineLimit(3)
                    .padding(.horizontal, 20)

                HStack(spacing: 6) {
                    Image(systemName: "pencil.circle.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 14))
                    Text("Start writing")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.orange.opacity(0.12))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .padding(20)
        }
    }
}

struct XYZWidget: Widget {
    let kind: String = "DailyPromptWidget"

    var body: some WidgetConfiguration {
        if #available(iOS 17.0, *) {
            return AppIntentConfiguration(kind: kind, intent: ConfigurationAppIntent.self, provider: AppIntentProvider()) { entry in
                XYZWidgetEntryView(entry: entry)
                    .containerBackground(for: .widget) { }
                    .widgetURL(URL(string: "easyjournal://new-entry?prompt=\(entry.dailyPrompt.prompt.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed) ?? "")"))
            }
            .configurationDisplayName("Daily Journal Prompt")
            .description("Get inspired with a new writing prompt every day!")
            .supportedFamilies([.systemMedium, .systemSmall])
        } else {
            return StaticConfiguration(kind: kind, provider: LegacyProvider()) { entry in
                XYZWidgetEntryView(entry: entry)
                    .edgesIgnoringSafeArea(.all)
                    .background(Color.red)
                    .widgetURL(URL(string: "easyjournal://new-entry?prompt=\(entry.dailyPrompt.prompt.addingPercentEncoding(withAllowedCharacters: CharacterSet.urlQueryAllowed) ?? "")"))
            }
            .configurationDisplayName("Daily Journal Prompt")
            .description("Get inspired with a new writing prompt every day!")
            .supportedFamilies([.systemMedium, .systemSmall])
        }
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Previews (iOS 16+ compatible)
struct XYZWidget_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            if #available(iOS 17.0, *) {
                XYZWidgetEntryView(entry: SimpleEntry(
                    date: .now,
                    configuration: ConfigurationAppIntent(),
                    dailyPrompt: DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
                    widgetData: WidgetDisplayData(motivationalMessage: "Make today memorable 🌟", hasWrittenToday: false, totalEntries: 67, todaysJournalEntry: nil, isNewUser: false, timeOfDay: .morning)
                ))
                .containerBackground(for: .widget) { }
                .previewContext(WidgetPreviewContext(family: .systemMedium))
                .previewDisplayName("iOS 17+ Morning Prompt")
            } else {
                XYZWidgetEntryView(entry: SimpleEntry(
                    date: .now,
                    configuration: EmptyConfiguration(),
                    dailyPrompt: DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
                    widgetData: WidgetDisplayData(motivationalMessage: "Make today memorable 🌟", hasWrittenToday: false, totalEntries: 67, todaysJournalEntry: nil, isNewUser: false, timeOfDay: .morning)
                ))

                .previewContext(WidgetPreviewContext(family: .systemMedium))
                .previewDisplayName("iOS 16 Morning Prompt")
            }
        }
    }
}

// MARK: - Previews (iOS 16+ compatible)
struct XYZWidgetSmall_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            if #available(iOS 17.0, *) {
                XYZWidgetEntryView(entry: SimpleEntry(
                    date: .now,
                    configuration: ConfigurationAppIntent(),
                    dailyPrompt: DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
                    widgetData: WidgetDisplayData(motivationalMessage: "Make today memorable 🌟", hasWrittenToday: false, totalEntries: 67, todaysJournalEntry: nil, isNewUser: false, timeOfDay: .morning)
                ))
                .containerBackground(for: .widget) { }
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("iOS 17+ Morning Small Prompt")
            } else {
                XYZWidgetEntryView(entry: SimpleEntry(
                    date: .now,
                    configuration: EmptyConfiguration(),
                    dailyPrompt: DailyPrompt(emoji: "🚀", prompt: "What's your wildest dream for the future?", category: .creativity),
                    widgetData: WidgetDisplayData(motivationalMessage: "Make today memorable 🌟", hasWrittenToday: false, totalEntries: 67, todaysJournalEntry: nil, isNewUser: false, timeOfDay: .morning)
                ))

                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("iOS 16 Morning Small Prompt")
            }
        }
    }
}
