//
//  SharedDataManager.swift
//  XYZWidget
//
//  Created by <PERSON> on 2025/7/4.
//

import Foundation

/// Simplified shared data manager for widget extension (without SQLite dependency)
/// This version uses UserDefaults for basic data sharing until SQLite can be properly configured
class SharedDataManager {
    static let shared = SharedDataManager()

    private let appGroupIdentifier = "group.cn.seungyu.journal"
    private var userDefaults: UserDefaults?

    private init() {
        userDefaults = UserDefaults(suiteName: appGroupIdentifier)
    }

    // MARK: - Simplified Journal Entry Access
    // Note: This is a simplified version for widget use
    // In a full implementation, this would access the shared SQLite database

    /// Get recent journal entries for widget display (simplified version)
    func getRecentJournalEntries(limit: Int = 5) -> [WidgetJournalEntry] {
        // For now, return empty array since we need to properly configure SQLite access
        // In a real implementation, this would read from the shared database
        return []
    }

    /// Get journal entry count (simplified version)
    func getJournalEntryCount() -> Int {
        // Return count from UserDefaults if available
        return userDefaults?.integer(forKey: "total_entries") ?? 0
    }

    /// Get most recent journal entry (simplified version)
    func getMostRecentEntry() -> WidgetJournalEntry? {
        return nil
    }

    /// Get entries for a specific date (simplified version)
    func getEntriesForDate(_ date: Date) -> [WidgetJournalEntry] {
        // For now, return mock data if it's today and user has written
        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            let hasWrittenToday = userDefaults?.bool(forKey: "has_written_today") ?? false
            if hasWrittenToday {
                // Return mock entry for demonstration
                return [WidgetJournalEntry(
                    id: "mock-entry",
                    date: date,
                    moodEmoji: "😊",
                    contentSnippet: "Had a wonderful day exploring the city and meeting new people. The weather was perfect for a walk in the park, and I discovered a charming little café...",
                    title: "Today's Adventure",
                    createdAt: date,
                    firstImageURL: nil
                )]
            }
        }
        return []
    }

    // MARK: - Data Sync Methods

    /// Update widget data from main app
    func updateWidgetData(hasWrittenToday: Bool, totalEntries: Int, lastEntryTitle: String?, lastEntryContent: String?) {
        userDefaults?.set(hasWrittenToday, forKey: "has_written_today")
        userDefaults?.set(totalEntries, forKey: "total_entries")
        userDefaults?.set(lastEntryTitle, forKey: "last_entry_title")
        userDefaults?.set(lastEntryContent, forKey: "last_entry_content")
        userDefaults?.set(Date(), forKey: "last_update")
    }
}

// MARK: - Widget Data Models

struct WidgetJournalEntry: Identifiable, Codable {
    let id: String
    let date: Date
    let moodEmoji: String
    let contentSnippet: String
    let title: String
    let createdAt: Date
    let firstImageURL: String?

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }

    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }

    var hasImage: Bool {
        return firstImageURL != nil && !firstImageURL!.isEmpty
    }
}
