import AVFoundation
import UIKit

class MediaHandler {
    // Save image to local file system
    static func saveImageToLocal(_ image: UIImage) -> URL? {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            return nil
        }

        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = "image_\(Date().timeIntervalSince1970).jpg"
        let fileURL = documentsDirectory.appendingPathComponent(fileName)

        do {
            try imageData.write(to: fileURL)
            return fileURL
        } catch {
            XYLog("Error saving image: \(error)")
            return nil
        }
    }

    // Generate video thumbnail
    static func generateVideoThumbnail(from url: URL, completion: @escaping (UIImage?) -> Void) {
        DispatchQueue.global(qos: .background).async {
            let asset = AVAsset(url: url)
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true

            // Try to get thumbnail at 1 second
            let time = CMTime(seconds: 1, preferredTimescale: 60)

            do {
                let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                completion(UIImage(cgImage: cgImage))
            } catch {
                XYLog("Error generating thumbnail: \(error)")
                completion(nil)
            }
        }
    }

    // If the url is a file url, remap it to a file url in the documents directory
    static func remapMediaURL(url: URL) -> URL {
        guard url.isFileURL else {
            return url
        }

        guard let documentsURL = SharedFileManager.shared.sharedDocumentsURL else {
            return url
        }

        let documentsDirectory = documentsURL
        let fileName = url.lastPathComponent
        let fileURL = documentsDirectory.appendingPathComponent(fileName)
        return fileURL
    }
}
