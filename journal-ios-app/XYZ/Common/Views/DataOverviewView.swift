import UIKit
import SnapKit

class DataOverviewView: UIView {
    // MARK: - Properties
    private let totalLabel = UILabel()
    private let valueLabel = UILabel()
    private let timeLabel = UILabel()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    // MARK: - Setup
    private func setupUI() {

        // Configure labels
        totalLabel.text = XYLocalize.XYLocalize("total_label")
        totalLabel.textColor = Colors.primary.dynamicColor()
        totalLabel.font = .systemFont(ofSize: 12, weight: .medium)
        totalLabel.textAlignment = .center

        valueLabel.font = .systemFont(ofSize: 32, weight: .bold)
        valueLabel.textColor = Colors.secondary.dynamicColor()
        valueLabel.textAlignment = .center

        timeLabel.font = .systemFont(ofSize: 14, weight: .medium)
        timeLabel.textColor = Colors.secondary.dynamicColor()
        timeLabel.textAlignment = .left

        // Add subviews
        addSubview(totalLabel)
        addSubview(valueLabel)
        addSubview(timeLabel)

        // Setup constraints
        totalLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(totalLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview()
        }

        timeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(totalLabel)
            make.leading.equalTo(totalLabel.snp.trailing).offset(5)
        }
    }

    // MARK: - Public Methods
    func updateContent(value: String, time: String) {
        valueLabel.text = value
        timeLabel.text = time
    }
}
